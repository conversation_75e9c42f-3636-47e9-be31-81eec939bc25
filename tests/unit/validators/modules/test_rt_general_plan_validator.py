"""
Test RT General Plan Module Validator functionality.

Tests for RTGeneralPlanValidator that validates DICOM PS3.3 C.8.8.9 RT General Plan Module
requirements including Type 1 elements, conditional requirements, enumerated values,
and sequence structure validation.
"""

import pytest
from pydicom import Dataset

from pyrt_dicom.validators.modules.rt_general_plan_validator import RTGeneralPlanValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.rt_enums import PlanIntent, RTplanGeometry, RTPlanRelationship


class TestRTGeneralPlanValidator:
    """Test RTGeneralPlanValidator functionality."""
    
    def test_valid_patient_based_plan(self):
        """Test validation of valid patient-based plan with all required elements."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanDate = "20240101"
        dataset.RTPlanTime = "120000"
        dataset.RTPlanGeometry = "PATIENT"
        
        # Add required structure set reference for PATIENT geometry
        structure_set_item = Dataset()
        structure_set_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.3"
        structure_set_item.ReferencedSOPInstanceUID = "*******.*******.9"
        dataset.ReferencedStructureSetSequence = [structure_set_item]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_valid_treatment_device_plan(self):
        """Test validation of valid treatment device plan."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Device Plan"
        dataset.RTPlanDate = "20240101"
        dataset.RTPlanTime = "120000"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_missing_required_elements(self):
        """Test validation fails for missing Type 1 elements."""
        dataset = Dataset()
        # Missing RTPlanLabel and RTPlanGeometry
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        
        # Check for specific error messages
        error_messages = [error for error in result.errors]
        assert any("RT Plan Label (300A,0002) is required" in msg for msg in error_messages)
        assert any("RT Plan Geometry (300A,000C) is required" in msg for msg in error_messages)
    
    def test_empty_required_elements(self):
        """Test validation fails for empty Type 1 elements."""
        dataset = Dataset()
        dataset.RTPlanLabel = ""  # Empty
        dataset.RTPlanGeometry = ""  # Empty
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        
        error_messages = [error for error in result.errors]
        assert any("RT Plan Label (300A,0002) cannot be empty" in msg for msg in error_messages)
        assert any("RT Plan Geometry (300A,000C) cannot be empty" in msg for msg in error_messages)
    
    def test_conditional_structure_set_requirement(self):
        """Test Type 1C conditional requirement for structure set reference."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Patient Plan"
        dataset.RTPlanGeometry = "PATIENT"
        # Missing ReferencedStructureSetSequence
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = [error for error in result.errors]
        assert any("Referenced Structure Set Sequence (300C,0060) is required when" in msg 
                  for msg in error_messages)
    
    def test_empty_structure_set_sequence(self):
        """Test validation fails for empty structure set sequence when required."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Patient Plan"
        dataset.RTPlanGeometry = "PATIENT"
        dataset.ReferencedStructureSetSequence = []  # Empty sequence
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = [error for error in result.errors]
        assert any("Referenced Structure Set Sequence (300C,0060) cannot be empty" in msg 
                  for msg in error_messages)
    
    def test_invalid_enumerated_values(self):
        """Test validation of invalid enumerated values."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "INVALID_GEOMETRY"  # Invalid value
        dataset.PlanIntent = "INVALID_INTENT"  # Invalid value
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        error_messages = [error for error in result.errors]
        assert any("RT Plan Geometry (300A,000C) value 'INVALID_GEOMETRY' is invalid" in msg 
                  for msg in error_messages)
        assert any("Plan Intent (300A,000A) value 'INVALID_INTENT' is invalid" in msg 
                  for msg in error_messages)
    
    def test_valid_enumerated_values(self):
        """Test validation passes for valid enumerated values."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = RTplanGeometry.PATIENT.value
        dataset.PlanIntent = PlanIntent.CURATIVE.value
        
        # Add required structure set reference
        structure_set_item = Dataset()
        structure_set_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.3"
        structure_set_item.ReferencedSOPInstanceUID = "*******.*******.9"
        dataset.ReferencedStructureSetSequence = [structure_set_item]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_sequence_validation_structure_set(self):
        """Test validation of Referenced Structure Set Sequence items."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "PATIENT"
        
        # Add incomplete structure set item
        incomplete_item = Dataset()
        # Missing ReferencedSOPClassUID and ReferencedSOPInstanceUID
        dataset.ReferencedStructureSetSequence = [incomplete_item]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        error_messages = [error for error in result.errors]
        assert any("Referenced SOP Class UID (0008,1150) is required" in msg 
                  for msg in error_messages)
        assert any("Referenced SOP Instance UID (0008,1155) is required" in msg 
                  for msg in error_messages)
    
    def test_sequence_validation_rt_plan(self):
        """Test validation of Referenced RT Plan Sequence items."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        # Add incomplete RT plan reference
        incomplete_plan = Dataset()
        incomplete_plan.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.5"
        # Missing ReferencedSOPInstanceUID and RTPlanRelationship
        dataset.ReferencedRTPlanSequence = [incomplete_plan]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        error_messages = [error for error in result.errors]
        assert any("Referenced SOP Instance UID (0008,1155) is required" in msg 
                  for msg in error_messages)
        assert any("RT Plan Relationship (300A,0055) is required" in msg 
                  for msg in error_messages)
    
    def test_invalid_rt_plan_relationship(self):
        """Test validation of invalid RT Plan Relationship values."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        # Add RT plan reference with invalid relationship
        plan_item = Dataset()
        plan_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.5"
        plan_item.ReferencedSOPInstanceUID = "*******.*******.10"
        plan_item.RTPlanRelationship = "INVALID_RELATIONSHIP"
        dataset.ReferencedRTPlanSequence = [plan_item]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        error_messages = [error for error in result.errors]
        assert any("RT Plan Relationship (300A,0055) value 'INVALID_RELATIONSHIP' is invalid" in msg 
                  for msg in error_messages)
    
    def test_treatment_site_code_sequence_validation(self):
        """Test validation of Treatment Site Code Sequence items."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        # Add incomplete treatment site code
        incomplete_code = Dataset()
        incomplete_code.CodeValue = "39607008"
        # Missing CodingSchemeDesignator and CodeMeaning
        dataset.TreatmentSiteCodeSequence = [incomplete_code]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        error_messages = [error for error in result.errors]
        assert any("Coding Scheme Designator (0008,0102) is required" in msg 
                  for msg in error_messages)
        assert any("Code Meaning (0008,0104) is required" in msg 
                  for msg in error_messages)
    
    def test_validation_config_options(self):
        """Test that validation configuration options work correctly."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "PATIENT"
        # Missing structure set reference (conditional requirement)
        dataset.PlanIntent = "INVALID_INTENT"  # Invalid enumerated value
        
        # Test with conditional validation disabled
        config_no_conditional = ValidationConfig(validate_conditional_requirements=False)
        result = RTGeneralPlanValidator.validate(dataset, config_no_conditional)
        
        # Should not have conditional requirement errors
        error_messages = [error for error in result.errors]
        assert not any("Referenced Structure Set Sequence" in msg for msg in error_messages)
        
        # Test with enumerated value checking disabled
        config_no_enum = ValidationConfig(check_enumerated_values=False)
        result = RTGeneralPlanValidator.validate(dataset, config_no_enum)
        
        # Should not have enumerated value errors
        error_messages = [error for error in result.errors]
        assert not any("Plan Intent (300A,000A) value 'INVALID_INTENT' is invalid" in msg 
                      for msg in error_messages)
    
    def test_rt_assertions_sequence_warning(self):
        """Test RT Assertions Sequence generates appropriate warnings."""
        dataset = Dataset()
        dataset.RTPlanLabel = "Test Plan"
        dataset.RTPlanGeometry = "TREATMENT_DEVICE"
        
        # Add incomplete assertion item
        incomplete_assertion = Dataset()
        # Missing assertion identification elements
        dataset.RTAssertionsSequence = [incomplete_assertion]
        
        result = RTGeneralPlanValidator.validate(dataset)
        
        assert len(result.warnings) >= 1
        warning_messages = [warning for warning in result.warnings]
        assert any("Should contain assertion identification elements" in msg 
                  for msg in warning_messages)
