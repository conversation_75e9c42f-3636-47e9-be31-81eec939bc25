"""RT Dose Module - DICOM PS3.3 C.8.8.3

The RT Dose Module is used to convey 2D or 3D radiation dose data generated 
from treatment planning systems or similar devices. The Attributes defined within 
the Module support dose for a single radiation beam or a group of beams comprising 
either a fraction group or a complete treatment plan.
"""
from datetime import datetime, date
from typing import Any
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.dose_enums import DoseUnits, DoseType
from ...enums.rt_enums import SpatialTransformOfDose, DoseSummationType, TissueHeterogeneityCorrection
from ...enums.image_enums import PhotometricInterpretation, PixelRepresentation
from ...validators.modules.rt_dose_validator import RTDoseValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_date_value, format_time_value, format_enum_value


class RTDoseModule(BaseModule):
    """RT Dose Module implementation for DICOM PS3.3 C.8.8.3.
    
    Uses composition-based architecture with internal dataset management.
    The RT Dose Module provides the mechanism to transmit a 3D array of dose data 
    as a set of 2D dose planes that may or may not be related to CT or MR image planes.
    
    Usage:
        # Create with required elements
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        # Add pixel data elements if present (Type 1C)
        dose.with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            dose_grid_scaling=0.001
        )
        
        # Add referenced RT plan (Type 1C based on dose summation type)
        dose.with_referenced_rt_plan(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.5",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9"
        )
        
        # Add optional elements
        dose.with_optional_elements(
            content_date="20240101",
            content_time="120000",
            instance_number="1",
            entity_long_label="Primary Dose Distribution",
            dose_comment="Calculated dose for treatment plan"
        )
        
        # Generate dataset for IOD integration
        dataset = dose.to_dataset()
        
        # Validate
        result = dose.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        dose_units: str | DoseUnits,
        dose_type: str | DoseType,
        dose_summation_type: str | DoseSummationType
    ) -> 'RTDoseModule':
        """Create RTDoseModule from all required (Type 1) data elements.
        
        Args:
            dose_units (str | DoseUnits): Units used to describe dose (3004,0002) Type 1
                Enumerated Values: GY (Gray) or RELATIVE (dose relative to implicit reference value)
            dose_type (str | DoseType): Type of dose (3004,0004) Type 1
                Defined Terms: PHYSICAL, EFFECTIVE, ERROR
            dose_summation_type (str | DoseSummationType): Type of dose summation (3004,000A) Type 1
                Defined Terms: PLAN, MULTI_PLAN, PLAN_OVERVIEW, FRACTION, BEAM, BRACHY, 
                FRACTION_SESSION, BEAM_SESSION, BRACHY_SESSION, CONTROL_POINT, RECORD
                
        Returns:
            RTDoseModule: New module instance with required data elements set
        """
        instance = cls()
        instance._dataset.DoseUnits = format_enum_value(dose_units)
        instance._dataset.DoseType = format_enum_value(dose_type)
        instance._dataset.DoseSummationType = format_enum_value(dose_summation_type)
        return instance
    
    def with_pixel_data_elements(
        self,
        samples_per_pixel: int,
        photometric_interpretation: str | PhotometricInterpretation,
        bits_allocated: int,
        bits_stored: int,
        high_bit: int,
        pixel_representation: int | PixelRepresentation,
        dose_grid_scaling: float,
        grid_frame_offset_vector: list[float] | None = None
    ) -> 'RTDoseModule':
        """Add pixel data elements (Type 1C - required if Pixel Data is present).
        
        Args:
            samples_per_pixel (int): Number of samples (planes) in this image (0028,0002) Type 1C
                Enumerated Values: 1 (for RT Dose)
            photometric_interpretation (str | PhotometricInterpretation): Interpretation of pixel data (0028,0004) Type 1C
                Enumerated Values: MONOCHROME2 (for RT Dose)
            bits_allocated (int): Number of bits allocated for each pixel sample (0028,0100) Type 1C
                Enumerated Values: 16 or 32 (for RT Dose)
            bits_stored (int): Number of bits stored for each pixel sample (0028,0101) Type 1C
                For RT Doses, shall be equal to Bits Allocated
            high_bit (int): Most significant bit for each pixel sample (0028,0102) Type 1C
                For RT Doses, shall be one less than the Value of Bits Stored
            pixel_representation (int | PixelRepresentation): Data representation of pixel samples (0028,0103) Type 1C
                0001H (two's complement) when Dose Type = ERROR, 0000H (unsigned) otherwise
            dose_grid_scaling (float): Scaling factor for dose grid data (3004,000E) Type 1C
                Required if Pixel Data is present
            grid_frame_offset_vector (list[float] | None): Dose image plane offsets (3004,000C) Type 1C
                Required if multi-frame pixel data are present and Frame Increment Pointer points to this
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        # Validate DICOM constraints for RT Dose
        if samples_per_pixel != 1:
            raise ValueError("Samples per Pixel must be 1 for RT Dose (DICOM PS3.3 C.8.8.3.4.1)")
        
        if format_enum_value(photometric_interpretation) != "MONOCHROME2":
            raise ValueError("Photometric Interpretation must be MONOCHROME2 for RT Dose (DICOM PS3.3 C.8.8.3.4.2)")
        
        if bits_allocated not in [16, 32]:
            raise ValueError("Bits Allocated must be 16 or 32 for RT Dose (DICOM PS3.3 C.8.8.3.4.3)")
        
        if bits_stored != bits_allocated:
            raise ValueError("Bits Stored must equal Bits Allocated for RT Dose (DICOM PS3.3 C.8.8.3.4.4)")
        
        if high_bit != (bits_stored - 1):
            raise ValueError("High Bit must be one less than Bits Stored for RT Dose (DICOM PS3.3 C.8.8.3.4.5)")
        
        # Validate Pixel Representation based on Dose Type
        dose_type = getattr(self._dataset, 'DoseType', '')
        expected_pixel_rep = 1 if dose_type == 'ERROR' else 0  # 1 = signed, 0 = unsigned
        if format_enum_value(pixel_representation) != expected_pixel_rep:
            raise ValueError(
                f"Pixel Representation must be {'1 (signed)' if dose_type == 'ERROR' else '0 (unsigned)'} "
                f"for Dose Type {dose_type} (DICOM PS3.3 C.8.8.3.4.6)"
            )
        
        self._dataset.SamplesPerPixel = samples_per_pixel
        self._dataset.PhotometricInterpretation = format_enum_value(photometric_interpretation)
        self._dataset.BitsAllocated = bits_allocated
        self._dataset.BitsStored = bits_stored
        self._dataset.HighBit = high_bit
        self._dataset.PixelRepresentation = format_enum_value(pixel_representation)
        self._dataset.DoseGridScaling = dose_grid_scaling
        
        if grid_frame_offset_vector is not None:
            self._dataset.GridFrameOffsetVector = grid_frame_offset_vector
            
        return self
    
    def with_referenced_rt_plan(
        self,
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_plan_overview_index: int | None = None,
        referenced_fraction_group_sequence: list[Dataset] | None = None
    ) -> 'RTDoseModule':
        """Add referenced RT plan (Type 1C - required for certain dose summation types).
        
        Required if Dose Summation Type is PLAN, MULTI_PLAN, FRACTION, BEAM, BRACHY,
        FRACTION_SESSION, BEAM_SESSION, BRACHY_SESSION or CONTROL_POINT.
        May be present if Dose Summation Type is PLAN_OVERVIEW.
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            referenced_plan_overview_index (int | None): Plan Overview Index (300C,0118) Type 1C
                Required if Plan Overview Sequence is present
            referenced_fraction_group_sequence (list[Dataset] | None): Referenced Fraction Groups (300C,0020) Type 1C
                Required if Dose Summation Type is FRACTION, BEAM, BRACHY, FRACTION_SESSION, BEAM_SESSION, BRACHY_SESSION or CONTROL_POINT
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        rt_plan_item = Dataset()
        rt_plan_item.ReferencedSOPClassUID = referenced_sop_class_uid
        rt_plan_item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        
        if referenced_plan_overview_index is not None:
            rt_plan_item.ReferencedPlanOverviewIndex = referenced_plan_overview_index
        if referenced_fraction_group_sequence is not None:
            rt_plan_item.ReferencedFractionGroupSequence = referenced_fraction_group_sequence
        
        self._dataset.ReferencedRTPlanSequence = [rt_plan_item]
        return self
    
    def with_referenced_spatial_registration(
        self,
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> 'RTDoseModule':
        """Add referenced spatial registration (Type 2C - required if spatial transform is RIGID or NON_RIGID).
        
        Required if Spatial Transform of Dose has a Value of RIGID or NON_RIGID.
        Zero or more Items shall be included in this Sequence.
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        registration_item = Dataset()
        registration_item.ReferencedSOPClassUID = referenced_sop_class_uid
        registration_item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        
        # Add to sequence or create new sequence
        if hasattr(self._dataset, 'ReferencedSpatialRegistrationSequence'):
            self._dataset.ReferencedSpatialRegistrationSequence.append(registration_item)
        else:
            self._dataset.ReferencedSpatialRegistrationSequence = [registration_item]
        
        return self
    
    def with_referenced_treatment_record(
        self,
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_beam_sequence: list[Dataset] | None = None
    ) -> 'RTDoseModule':
        """Add referenced treatment record (Type 1C - required if dose summation type is RECORD).
        
        Required if Dose Summation Type is RECORD.
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            referenced_beam_sequence (list[Dataset] | None): Referenced Beams (300C,0004) Type 1C
                Required if dose does not apply to complete RT Beams Treatment Record
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        treatment_record_item = Dataset()
        treatment_record_item.ReferencedSOPClassUID = referenced_sop_class_uid
        treatment_record_item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        
        if referenced_beam_sequence is not None:
            treatment_record_item.ReferencedBeamSequence = referenced_beam_sequence
        
        self._dataset.ReferencedTreatmentRecordSequence = [treatment_record_item]
        return self
    
    def with_plan_overview(
        self,
        plan_overview_index: int,
        rt_plan_label: str,
        number_of_fractions_included: int,
        treatment_site: str | None = None,
        treatment_site_code_sequence: list[Dataset] | None = None,
        prescription_overview_sequence: list[Dataset] | None = None,
        referenced_structure_set_sequence: list[Dataset] | None = None,
        referenced_image_sequence: list[Dataset] | None = None,
        current_fraction_number: int | None = None
    ) -> 'RTDoseModule':
        """Add plan overview (Type 1C - required if dose summation type is PLAN_OVERVIEW).
        
        Args:
            plan_overview_index (int): Index of the Plan Overview within this Sequence (300C,0117)
            rt_plan_label (str): User-defined label of treatment plan (300A,0002) Type 2
            number_of_fractions_included (int): Number of fractions included in this RT Dose Instance (300C,0119)
            treatment_site (str | None): Free-text label describing anatomical treatment site (3010,0077) Type 2
            treatment_site_code_sequence (list[Dataset] | None): Coded description of treatment site (3010,0078) Type 2
            prescription_overview_sequence (list[Dataset] | None): Prescription parameters (300C,0114) Type 2
            referenced_structure_set_sequence (list[Dataset] | None): Referenced Structure Set (300C,0060) Type 1C
            referenced_image_sequence (list[Dataset] | None): Referenced Images (0008,1140) Type 1C
            current_fraction_number (int | None): Current fraction number (3008,0022) Type 1C (for RECORD)
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        plan_overview_item = Dataset()
        plan_overview_item.PlanOverviewIndex = plan_overview_index
        plan_overview_item.RTPlanLabel = rt_plan_label
        plan_overview_item.NumberOfFractionsIncluded = number_of_fractions_included
        
        if treatment_site is not None:
            plan_overview_item.TreatmentSite = treatment_site
        if treatment_site_code_sequence is not None:
            plan_overview_item.TreatmentSiteCodeSequence = treatment_site_code_sequence
        if prescription_overview_sequence is not None:
            plan_overview_item.PrescriptionOverviewSequence = prescription_overview_sequence
        if referenced_structure_set_sequence is not None:
            plan_overview_item.ReferencedStructureSetSequence = referenced_structure_set_sequence
        if referenced_image_sequence is not None:
            plan_overview_item.ReferencedImageSequence = referenced_image_sequence
        if current_fraction_number is not None:
            plan_overview_item.CurrentFractionNumber = current_fraction_number
        
        self._dataset.PlanOverviewSequence = [plan_overview_item]
        return self
    
    def with_optional_elements(
        self,
        content_date: str | datetime | date | None = None,
        content_time: str | datetime | None = None,
        spatial_transform_of_dose: str | SpatialTransformOfDose | None = None,
        instance_number: str | int | None = None,
        entity_long_label: str | None = None,
        dose_comment: str | None = None,
        normalization_point: list[float] | None = None,
        tissue_heterogeneity_correction: str | list[str] | TissueHeterogeneityCorrection | None = None,
        recommended_isodose_level_sequence: list[Dataset] | None = None,
        derivation_code_sequence: list[Dataset] | None = None,
        referenced_instance_sequence: list[Dataset] | None = None
    ) -> 'RTDoseModule':
        """Add optional (Type 3) elements.
        
        Args:
            content_date (str | datetime | date | None): Date content was created (0008,0023) Type 3
            content_time (str | datetime | None): Time content was created (0008,0033) Type 3
            spatial_transform_of_dose (str | SpatialTransformOfDose | None): Use of transformation (3004,0005) Type 3
                Defined Terms: NONE (no transformation), RIGID (rigid transform), NON_RIGID (any other transform)
            instance_number (str | int | None): Number identifying this object Instance (0020,0013) Type 3
            entity_long_label (str | None): User-defined label for dose data (3010,0038) Type 3
            dose_comment (str | None): User-defined comments for dose data (3004,0006) Type 3
            normalization_point (list[float] | None): Coordinates of normalization point (3004,0008) Type 3
                Coordinates (x, y, z) in the patient-based coordinate system (mm)
            tissue_heterogeneity_correction (str | list[str] | TissueHeterogeneityCorrection | None): Patient heterogeneity characteristics (3004,0014) Type 3
                Enumerated Values: IMAGE (image data), ROI_OVERRIDE (ROI densities override), WATER (water equivalent)
            recommended_isodose_level_sequence (list[Dataset] | None): Recommended isodose levels (3004,0016) Type 3
            derivation_code_sequence (list[Dataset] | None): How dose was derived (0008,9215) Type 3
            referenced_instance_sequence (list[Dataset] | None): SOP Instances used to derive dose (0008,114A) Type 3
            
        Returns:
            RTDoseModule: Self for method chaining
        """
        if content_date is not None:
            self._dataset.ContentDate = format_date_value(content_date)
        if content_time is not None:
            self._dataset.ContentTime = format_time_value(content_time)
        if spatial_transform_of_dose is not None:
            self._dataset.SpatialTransformOfDose = format_enum_value(spatial_transform_of_dose)
        if instance_number is not None:
            self._dataset.InstanceNumber = str(instance_number)
        if entity_long_label is not None:
            self._dataset.EntityLongLabel = entity_long_label
        if dose_comment is not None:
            self._dataset.DoseComment = dose_comment
        if normalization_point is not None:
            if len(normalization_point) != 3:
                raise ValueError("Normalization Point must have exactly 3 coordinates (x, y, z)")
            self._dataset.NormalizationPoint = normalization_point
        if tissue_heterogeneity_correction is not None:
            self._dataset.TissueHeterogeneityCorrection = format_enum_value(tissue_heterogeneity_correction)
        if recommended_isodose_level_sequence is not None:
            self._dataset.RecommendedIsodoseLevelSequence = recommended_isodose_level_sequence
        if derivation_code_sequence is not None:
            self._dataset.DerivationCodeSequence = derivation_code_sequence
        if referenced_instance_sequence is not None:
            self._dataset.ReferencedInstanceSequence = referenced_instance_sequence
        return self
    
    @staticmethod
    def create_recommended_isodose_level_item(
        dose_value: float,
        recommended_display_cielab_value: list[int]
    ) -> Dataset:
        """Create recommended isodose level sequence item.

        Args:
            dose_value (float): Value for the isodose (3004,0012)
            recommended_display_cielab_value (list[int]): Default color triplet (0062,000D)
                CIELab values encoded as integers

        Returns:
            Dataset: Recommended isodose level sequence item
        """
        if len(recommended_display_cielab_value) != 3:
            raise ValueError("Recommended Display CIELab Value must have exactly 3 values")
        
        item = Dataset()
        item.DoseValue = dose_value
        item.RecommendedDisplayCIELabValue = recommended_display_cielab_value
        return item
    
    @staticmethod
    def create_referenced_fraction_group_item(
        referenced_fraction_group_number: int,
        referenced_beam_sequence: list[Dataset] | None = None,
        referenced_brachy_application_setup_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create referenced fraction group sequence item.

        Args:
            referenced_fraction_group_number (int): Fraction Group Number (300C,0022)
            referenced_beam_sequence (list[Dataset] | None): Referenced Beams (300C,0004) Type 1C
                Required if Dose Summation Type is BEAM, BEAM_SESSION or CONTROL_POINT
            referenced_brachy_application_setup_sequence (list[Dataset] | None): Referenced Brachy Setups (300C,000A) Type 1C
                Required if Dose Summation Type is BRACHY or BRACHY_SESSION

        Returns:
            Dataset: Referenced fraction group sequence item
        """
        item = Dataset()
        item.ReferencedFractionGroupNumber = referenced_fraction_group_number
        if referenced_beam_sequence is not None:
            item.ReferencedBeamSequence = referenced_beam_sequence
        if referenced_brachy_application_setup_sequence is not None:
            item.ReferencedBrachyApplicationSetupSequence = referenced_brachy_application_setup_sequence
        return item
    
    @staticmethod
    def create_referenced_beam_item(
        referenced_beam_number: int,
        referenced_control_point_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create referenced beam sequence item.
        
        Args:
            referenced_beam_number (int): Referenced Beam Number (300C,0006)
            referenced_control_point_sequence (list[Dataset] | None): Referenced Control Points (300C,00F2) Type 1C
                Required if Dose Summation Type is CONTROL_POINT
        
        Returns:
            Dataset: Referenced beam sequence item
        """
        item = Dataset()
        item.ReferencedBeamNumber = referenced_beam_number
        if referenced_control_point_sequence is not None:
            item.ReferencedControlPointSequence = referenced_control_point_sequence
        return item
    
    @staticmethod
    def create_referenced_control_point_item(
        referenced_start_control_point_index: int,
        referenced_stop_control_point_index: int
    ) -> Dataset:
        """Create referenced control point sequence item.
        
        Args:
            referenced_start_control_point_index (int): Referenced Start Control Point Index (300C,00F4)
            referenced_stop_control_point_index (int): Referenced Stop Control Point Index (300C,00F6)
                Must be immediately following the start control point index
        
        Returns:
            Dataset: Referenced control point sequence item
        """
        if referenced_stop_control_point_index != (referenced_start_control_point_index + 1):
            raise ValueError(
                "Referenced Stop Control Point Index must be immediately following "
                "the Referenced Start Control Point Index"
            )
        
        item = Dataset()
        item.ReferencedStartControlPointIndex = referenced_start_control_point_index
        item.ReferencedStopControlPointIndex = referenced_stop_control_point_index
        return item
    
    @staticmethod
    def create_prescription_overview_item(
        total_prescription_dose: float,
        entity_long_label: str | None = None,
        referenced_roi_number: int | None = None
    ) -> Dataset:
        """Create prescription overview sequence item.
        
        Args:
            total_prescription_dose (float): Total prescription dose in Gy (300C,0115)
            entity_long_label (str | None): Label identifying the Prescription Overview (3010,0038) Type 1C
                Required if Prescription Overview Sequence has more than one Item
            referenced_roi_number (int | None): ROI Number for prescription (3006,0084) Type 3
        
        Returns:
            Dataset: Prescription overview sequence item
        """
        item = Dataset()
        item.TotalPrescriptionDose = total_prescription_dose
        if entity_long_label is not None:
            item.EntityLongLabel = entity_long_label
        if referenced_roi_number is not None:
            item.ReferencedROINumber = referenced_roi_number
        return item
    
    @property
    def has_pixel_data(self) -> bool:
        """Check if pixel data elements are present.
        
        Returns:
            bool: True if pixel data related elements are present
        """
        return (hasattr(self._dataset, 'SamplesPerPixel') and 
                hasattr(self._dataset, 'PhotometricInterpretation') and
                hasattr(self._dataset, 'BitsAllocated'))
    
    @property
    def has_spatial_transform(self) -> bool:
        """Check if spatial transformation is used.
        
        Returns:
            bool: True if spatial transform is not NONE
        """
        spatial_transform = getattr(self._dataset, 'SpatialTransformOfDose', 'NONE')
        return spatial_transform != 'NONE'
    
    @property
    def requires_rt_plan_reference(self) -> bool:
        """Check if RT plan reference is required based on dose summation type.
        
        Returns:
            bool: True if dose summation type requires RT plan reference
        """
        dose_summation_type = getattr(self._dataset, 'DoseSummationType', '')
        return dose_summation_type in [
            'PLAN', 'MULTI_PLAN', 'FRACTION', 'BEAM', 'BRACHY',
            'FRACTION_SESSION', 'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT'
        ]
    
    @property
    def requires_fraction_group_reference(self) -> bool:
        """Check if fraction group reference is required based on dose summation type.
        
        Returns:
            bool: True if dose summation type requires fraction group reference
        """
        dose_summation_type = getattr(self._dataset, 'DoseSummationType', '')
        return dose_summation_type in [
            'FRACTION', 'BEAM', 'BRACHY', 'FRACTION_SESSION',
            'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT'
        ]
    
    @property
    def requires_treatment_record_reference(self) -> bool:
        """Check if treatment record reference is required based on dose summation type.
        
        Returns:
            bool: True if dose summation type requires treatment record reference
        """
        dose_summation_type = getattr(self._dataset, 'DoseSummationType', '')
        return dose_summation_type == 'RECORD'
    
    @property
    def requires_plan_overview(self) -> bool:
        """Check if plan overview is required based on dose summation type.
        
        Returns:
            bool: True if dose summation type requires plan overview
        """
        dose_summation_type = getattr(self._dataset, 'DoseSummationType', '')
        return dose_summation_type == 'PLAN_OVERVIEW'
    
    @property
    def requires_spatial_registration(self) -> bool:
        """Check if spatial registration is required based on spatial transform.
        
        Returns:
            bool: True if spatial transform requires registration reference
        """
        spatial_transform = getattr(self._dataset, 'SpatialTransformOfDose', 'NONE')
        return spatial_transform in ['RIGID', 'NON_RIGID']
    
    @property
    def requires_structure_set_or_image_reference(self) -> bool:
        """Check if structure set or image reference is required.
        
        Either Referenced Structure Set Sequence or Referenced Image Sequence must be present.
        
        Returns:
            bool: True if neither reference is present
        """
        has_structure_set = hasattr(self._dataset, 'ReferencedStructureSetSequence')
        has_image = hasattr(self._dataset, 'ReferencedImageSequence')
        return not (has_structure_set or has_image)
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured with required elements.
        
        Returns:
            bool: True if all required Type 1 elements are present
        """
        return (hasattr(self._dataset, 'DoseUnits') and 
                hasattr(self._dataset, 'DoseType') and 
                hasattr(self._dataset, 'DoseSummationType'))
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Dose Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return RTDoseValidator.validate(self._dataset, config)