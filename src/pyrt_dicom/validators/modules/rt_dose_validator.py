"""RT Dose Module DICOM validation - PS3.3 C.8.8.3

RT Dose Module validator provides comprehensive validation of all DICOM requirements
including conditional logic, enumerated values, sequence structures, and cross-field
dependencies according to DICOM PS3.3 Section C.8.8.3.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.dose_enums import DoseUnits, DoseType
from ...enums.rt_enums import SpatialTransformOfDose, DoseSummationType, TissueHeterogeneityCorrection
from ...enums.image_enums import PhotometricInterpretation, PixelRepresentation


class RTDoseValidator(BaseValidator):
    """Validator for DICOM RT Dose Module (PS3.3 C.8.8.3).
    
    Validates all Type 1, Type 2, Type 3, Type 1C, and Type 2C requirements
    according to the DICOM standard including complex conditional logic,
    enumerated values, and cross-field dependencies.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Dose Module requirements on any pydicom Dataset.
        
        Args:
            dataset (Dataset): pydicom Dataset to validate
            config (ValidationConfig | None): Validation configuration options
            
        Returns:
            ValidationResult: Validation result with errors and warnings lists
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 (required) elements
        RTDoseValidator._validate_type1_elements(dataset, result)
        
        # Validate Type 1C and 2C conditional requirements
        if config.validate_conditional_requirements:
            RTDoseValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTDoseValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTDoseValidator._validate_sequence_requirements(dataset, result)
        
        # Validate pixel data consistency and DICOM constraints
        RTDoseValidator._validate_pixel_data_consistency(dataset, result)
        
        # Validate cross-field dependencies and complex logic
        RTDoseValidator._validate_cross_field_dependencies(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type1_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) elements."""
        required_elements = [
            ('DoseUnits', '3004,0002', 'Dose Units'),
            ('DoseType', '3004,0004', 'Dose Type'),
            ('DoseSummationType', '3004,000A', 'Dose Summation Type')
        ]
        
        for attr_name, tag, description in required_elements:
            if not hasattr(dataset, attr_name):
                result.add_error(
                    f"{description} ({tag}) is required for RT Dose Module (Type 1)"
                )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements."""
        
        # === PIXEL DATA CONDITIONAL REQUIREMENTS (Type 1C) ===
        # All pixel data elements required if Pixel Data is present
        has_pixel_data = hasattr(dataset, 'PixelData')
        if has_pixel_data:
            pixel_elements = [
                ('SamplesPerPixel', '0028,0002', 'Samples per Pixel'),
                ('PhotometricInterpretation', '0028,0004', 'Photometric Interpretation'),
                ('BitsAllocated', '0028,0100', 'Bits Allocated'),
                ('BitsStored', '0028,0101', 'Bits Stored'),
                ('HighBit', '0028,0102', 'High Bit'),
                ('PixelRepresentation', '0028,0103', 'Pixel Representation'),
                ('DoseGridScaling', '3004,000E', 'Dose Grid Scaling')
            ]
            
            for attr_name, tag, description in pixel_elements:
                if not hasattr(dataset, attr_name):
                    result.add_error(
                        f"{description} ({tag}) is required when Pixel Data (7FE0,0010) is present (Type 1C)"
                    )
        
        # === DOSE SUMMATION TYPE CONDITIONAL REQUIREMENTS ===
        dose_summation_type = getattr(dataset, 'DoseSummationType', '')
        
        # Referenced RT Plan Sequence (Type 1C)
        rt_plan_required_types = [
            'PLAN', 'MULTI_PLAN', 'FRACTION', 'BEAM', 'BRACHY',
            'FRACTION_SESSION', 'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT'
        ]
        if dose_summation_type in rt_plan_required_types:
            if not hasattr(dataset, 'ReferencedRTPlanSequence'):
                result.add_error(
                    f"Referenced RT Plan Sequence (300C,0002) is required when "
                    f"Dose Summation Type is {dose_summation_type} (Type 1C)"
                )
            elif len(getattr(dataset, 'ReferencedRTPlanSequence', [])) == 0:
                result.add_error(
                    f"Referenced RT Plan Sequence (300C,0002) must contain at least one item when "
                    f"Dose Summation Type is {dose_summation_type} (Type 1C)"
                )
        
        # Plan Overview Sequence (Type 1C)
        if dose_summation_type == 'PLAN_OVERVIEW':
            if not hasattr(dataset, 'PlanOverviewSequence'):
                result.add_error(
                    "Plan Overview Sequence (300C,0116) is required when "
                    "Dose Summation Type is PLAN_OVERVIEW (Type 1C)"
                )
        
        # Referenced Treatment Record Sequence (Type 1C)
        if dose_summation_type == 'RECORD':
            if not hasattr(dataset, 'ReferencedTreatmentRecordSequence'):
                result.add_error(
                    "Referenced Treatment Record Sequence (3008,0030) is required when "
                    "Dose Summation Type is RECORD (Type 1C)"
                )
        
        # === SPATIAL TRANSFORMATION CONDITIONAL REQUIREMENTS ===
        # Referenced Spatial Registration Sequence (Type 2C)
        spatial_transform = getattr(dataset, 'SpatialTransformOfDose', '')
        if spatial_transform in ['RIGID', 'NON_RIGID']:
            if not hasattr(dataset, 'ReferencedSpatialRegistrationSequence'):
                result.add_error(
                    f"Referenced Spatial Registration Sequence (0070,0404) is required when "
                    f"Spatial Transform of Dose is {spatial_transform} (Type 2C)"
                )
        
        # === MULTI-FRAME CONDITIONAL REQUIREMENTS ===
        # Grid Frame Offset Vector (Type 1C)
        has_multi_frame = hasattr(dataset, 'NumberOfFrames') and getattr(dataset, 'NumberOfFrames', 1) > 1
        if has_multi_frame and has_pixel_data:
            frame_increment_pointer = getattr(dataset, 'FrameIncrementPointer', None)
            if frame_increment_pointer:
                # Check if Frame Increment Pointer references Grid Frame Offset Vector
                pointer_str = str(frame_increment_pointer)
                if '3004000C' in pointer_str or 'GridFrameOffsetVector' in pointer_str:
                    if not hasattr(dataset, 'GridFrameOffsetVector'):
                        result.add_error(
                            "Grid Frame Offset Vector (3004,000C) is required for multi-frame dose data "
                            "when Frame Increment Pointer (0028,0009) points to it (Type 1C)"
                        )
        
        # === PLAN OVERVIEW SEQUENCE CONDITIONAL REQUIREMENTS ===
        plan_overview_seq = getattr(dataset, 'PlanOverviewSequence', [])
        for i, plan_overview in enumerate(plan_overview_seq):
            # Number of Fractions Included (Type 1C)
            if dose_summation_type in ['PLAN_OVERVIEW', 'PLAN', 'MULTI_PLAN']:
                if not plan_overview.get('NumberOfFractionsIncluded'):
                    result.add_error(
                        f"Plan Overview Sequence item {i}: Number of Fractions Included (300C,0119) "
                        f"is required when Dose Summation Type is {dose_summation_type} (Type 1C)"
                    )
            
            # Current Fraction Number (Type 1C)
            if dose_summation_type == 'RECORD':
                if not plan_overview.get('CurrentFractionNumber'):
                    result.add_error(
                        f"Plan Overview Sequence item {i}: Current Fraction Number (3008,0022) "
                        "is required when Dose Summation Type is RECORD (Type 1C)"
                    )
            
            # Referenced Plan Overview Index (Type 1C)
            if hasattr(dataset, 'ReferencedRTPlanSequence'):
                rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
                for j, rt_plan in enumerate(rt_plan_seq):
                    if not rt_plan.get('ReferencedPlanOverviewIndex') and plan_overview_seq:
                        result.add_error(
                            f"Referenced RT Plan Sequence item {j}: Referenced Plan Overview Index (300C,0118) "
                            "is required when Plan Overview Sequence is present (Type 1C)"
                        )
            
            # Structure Set OR Image Reference (Type 1C - Either/Or)
            has_structure_set = plan_overview.get('ReferencedStructureSetSequence')
            has_image_ref = plan_overview.get('ReferencedImageSequence')
            if not has_structure_set and not has_image_ref:
                result.add_error(
                    f"Plan Overview Sequence item {i}: Either Referenced Structure Set Sequence (300C,0060) "
                    "OR Referenced Image Sequence (0008,1140) must be present (Type 1C)"
                )
        
        # === PRESCRIPTION OVERVIEW SEQUENCE CONDITIONALS ===
        # Entity Long Label required if multiple prescription overview items
        for plan_overview in plan_overview_seq:
            prescription_seq = plan_overview.get('PrescriptionOverviewSequence', [])
            if len(prescription_seq) > 1:
                for i, prescription in enumerate(prescription_seq):
                    if not prescription.get('EntityLongLabel'):
                        result.add_error(
                            f"Prescription Overview Sequence item {i}: Entity Long Label (3010,0038) "
                            "is required when Prescription Overview Sequence has more than one item (Type 1C)"
                        )
        
        # === REFERENCED FRACTION GROUP SEQUENCE CONDITIONALS ===
        fraction_group_required_types = [
            'FRACTION', 'BEAM', 'BRACHY', 'FRACTION_SESSION',
            'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT'
        ]
        if dose_summation_type in fraction_group_required_types:
            rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
            for i, rt_plan in enumerate(rt_plan_seq):
                if not rt_plan.get('ReferencedFractionGroupSequence'):
                    result.add_error(
                        f"Referenced RT Plan Sequence item {i}: Referenced Fraction Group Sequence (300C,0020) "
                        f"is required when Dose Summation Type is {dose_summation_type} (Type 1C)"
                    )
                
                # Validate fraction group contents based on dose summation type
                fraction_groups = rt_plan.get('ReferencedFractionGroupSequence', [])
                for j, fraction_group in enumerate(fraction_groups):
                    # Referenced Beam Sequence (Type 1C)
                    beam_required_types = ['BEAM', 'BEAM_SESSION', 'CONTROL_POINT']
                    if dose_summation_type in beam_required_types:
                        if not fraction_group.get('ReferencedBeamSequence'):
                            result.add_error(
                                f"Referenced Fraction Group Sequence item {j}: Referenced Beam Sequence (300C,0004) "
                                f"is required when Dose Summation Type is {dose_summation_type} (Type 1C)"
                            )
                    
                    # Referenced Brachy Application Setup Sequence (Type 1C) 
                    brachy_required_types = ['BRACHY', 'BRACHY_SESSION']
                    if dose_summation_type in brachy_required_types:
                        if not fraction_group.get('ReferencedBrachyApplicationSetupSequence'):
                            result.add_error(
                                f"Referenced Fraction Group Sequence item {j}: "
                                "Referenced Brachy Application Setup Sequence (300C,000A) "
                                f"is required when Dose Summation Type is {dose_summation_type} (Type 1C)"
                            )
                    
                    # Referenced Control Point Sequence (Type 1C)
                    if dose_summation_type == 'CONTROL_POINT':
                        beam_seq = fraction_group.get('ReferencedBeamSequence', [])
                        for k, beam in enumerate(beam_seq):
                            if not beam.get('ReferencedControlPointSequence'):
                                result.add_error(
                                    f"Referenced Beam Sequence item {k}: Referenced Control Point Sequence (300C,00F2) "
                                    "is required when Dose Summation Type is CONTROL_POINT (Type 1C)"
                                )
        
        # === TREATMENT RECORD CONDITIONAL REQUIREMENTS ===
        treatment_record_seq = getattr(dataset, 'ReferencedTreatmentRecordSequence', [])
        for i, treatment_record in enumerate(treatment_record_seq):
            # Referenced Beam Sequence (Type 1C)
            # Required if dose does not apply to complete RT Beams Treatment Record
            # This is a clinical decision, so we just warn if missing
            if not treatment_record.get('ReferencedBeamSequence'):
                result.add_warning(
                    f"Referenced Treatment Record Sequence item {i}: Referenced Beam Sequence (300C,0004) "
                    "may be required if dose does not apply to complete RT Beams Treatment Record (Type 1C)"
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Dose Units (3004,0002) - Type 1
        dose_units = getattr(dataset, 'DoseUnits', '')
        if dose_units:
            valid_units = [units.value for units in DoseUnits]
            BaseValidator.validate_enumerated_value(
                dose_units, valid_units,
                "Dose Units (3004,0002)", result
            )
        
        # Dose Type (3004,0004) - Type 1
        dose_type = getattr(dataset, 'DoseType', '')
        if dose_type:
            valid_types = [dtype.value for dtype in DoseType]
            BaseValidator.validate_enumerated_value(
                dose_type, valid_types,
                "Dose Type (3004,0004)", result
            )
        
        # Dose Summation Type (3004,000A) - Type 1
        dose_summation_type = getattr(dataset, 'DoseSummationType', '')
        if dose_summation_type:
            valid_summation_types = [stype.value for stype in DoseSummationType]
            BaseValidator.validate_enumerated_value(
                dose_summation_type, valid_summation_types,
                "Dose Summation Type (3004,000A)", result
            )
        
        # Spatial Transform of Dose (3004,0005) - Type 3
        spatial_transform = getattr(dataset, 'SpatialTransformOfDose', '')
        if spatial_transform:
            valid_transforms = [transform.value for transform in SpatialTransformOfDose]
            BaseValidator.validate_enumerated_value(
                spatial_transform, valid_transforms,
                "Spatial Transform of Dose (3004,0005)", result
            )
        
        # Tissue Heterogeneity Correction (3004,0014) - Type 3
        tissue_correction = getattr(dataset, 'TissueHeterogeneityCorrection', '')
        if tissue_correction:
            valid_corrections = [corr.value for corr in TissueHeterogeneityCorrection]
            BaseValidator.validate_enumerated_value(
                tissue_correction, valid_corrections,
                "Tissue Heterogeneity Correction (3004,0014)", result
            )
        
        # Photometric Interpretation (0028,0004) - Type 1C with RT Dose constraint
        photometric_interp = getattr(dataset, 'PhotometricInterpretation', '')
        if photometric_interp:
            if photometric_interp != 'MONOCHROME2':
                result.add_error(
                    "Photometric Interpretation (0028,0004) must be MONOCHROME2 for RT Dose "
                    "(DICOM PS3.3 C.8.8.3.4.2)"
                )
        
        # Samples per Pixel (0028,0002) - Type 1C with RT Dose constraint
        samples_per_pixel = getattr(dataset, 'SamplesPerPixel', None)
        if samples_per_pixel is not None:
            if samples_per_pixel != 1:
                result.add_error(
                    "Samples per Pixel (0028,0002) must be 1 for RT Dose "
                    "(DICOM PS3.3 C.8.8.3.4.1)"
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Referenced RT Plan Sequence validation
        ref_rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
        for i, plan_item in enumerate(ref_rt_plan_seq):
            if not plan_item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not plan_item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Referenced Spatial Registration Sequence validation
        ref_spatial_reg_seq = getattr(dataset, 'ReferencedSpatialRegistrationSequence', [])
        for i, reg_item in enumerate(ref_spatial_reg_seq):
            if not reg_item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced Spatial Registration Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not reg_item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced Spatial Registration Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Referenced Treatment Record Sequence validation
        ref_treatment_record_seq = getattr(dataset, 'ReferencedTreatmentRecordSequence', [])
        for i, record_item in enumerate(ref_treatment_record_seq):
            if not record_item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced Treatment Record Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not record_item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced Treatment Record Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Recommended Isodose Level Sequence validation
        isodose_seq = getattr(dataset, 'RecommendedIsodoseLevelSequence', [])
        for i, isodose_item in enumerate(isodose_seq):
            if not isodose_item.get('DoseValue'):
                result.add_error(
                    f"Recommended Isodose Level Sequence item {i}: "
                    "Dose Value (3004,0012) is required"
                )
            if not isodose_item.get('RecommendedDisplayCIELabValue'):
                result.add_error(
                    f"Recommended Isodose Level Sequence item {i}: "
                    "Recommended Display CIELab Value (0062,000D) is required"
                )
            else:
                # Validate CIELab value has exactly 3 components
                cielab_value = isodose_item.get('RecommendedDisplayCIELabValue')
                if len(cielab_value) != 3:
                    result.add_error(
                        f"Recommended Isodose Level Sequence item {i}: "
                        "Recommended Display CIELab Value (0062,000D) must have exactly 3 values"
                    )
        
        # Plan Overview Sequence validation
        plan_overview_seq = getattr(dataset, 'PlanOverviewSequence', [])
        for i, plan_item in enumerate(plan_overview_seq):
            if not plan_item.get('PlanOverviewIndex'):
                result.add_error(
                    f"Plan Overview Sequence item {i}: "
                    "Plan Overview Index (300C,0117) is required"
                )
            if not plan_item.get('RTPlanLabel'):
                result.add_error(
                    f"Plan Overview Sequence item {i}: "
                    "RT Plan Label (300A,0002) is required (Type 2)"
                )
            
            # Validate prescription overview sequence if present
            prescription_seq = plan_item.get('PrescriptionOverviewSequence', [])
            for j, prescription in enumerate(prescription_seq):
                if not prescription.get('TotalPrescriptionDose'):
                    result.add_error(
                        f"Plan Overview Sequence item {i}, Prescription Overview Sequence item {j}: "
                        "Total Prescription Dose (300C,0115) is required"
                    )
        
        # Referenced Control Point Sequence validation
        # Validate within Referenced Beam Sequence
        for plan_item in ref_rt_plan_seq:
            fraction_groups = plan_item.get('ReferencedFractionGroupSequence', [])
            for fraction_group in fraction_groups:
                beams = fraction_group.get('ReferencedBeamSequence', [])
                for i, beam in enumerate(beams):
                    control_point_seq = beam.get('ReferencedControlPointSequence', [])
                    for j, control_point in enumerate(control_point_seq):
                        start_index = control_point.get('ReferencedStartControlPointIndex')
                        stop_index = control_point.get('ReferencedStopControlPointIndex')
                        
                        if start_index is None:
                            result.add_error(
                                f"Referenced Control Point Sequence item {j}: "
                                "Referenced Start Control Point Index (300C,00F4) is required"
                            )
                        if stop_index is None:
                            result.add_error(
                                f"Referenced Control Point Sequence item {j}: "
                                "Referenced Stop Control Point Index (300C,00F6) is required"
                            )
                        
                        # Validate stop index is immediately following start index
                        if start_index is not None and stop_index is not None:
                            if stop_index != start_index + 1:
                                result.add_error(
                                    f"Referenced Control Point Sequence item {j}: "
                                    "Referenced Stop Control Point Index must be immediately following "
                                    "the Referenced Start Control Point Index"
                                )
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel data consistency and DICOM constraints for RT Dose."""
        
        # Validate bits allocated/stored/high bit consistency
        bits_allocated = getattr(dataset, 'BitsAllocated', None)
        bits_stored = getattr(dataset, 'BitsStored', None)
        high_bit = getattr(dataset, 'HighBit', None)
        
        if bits_allocated is not None:
            # Validate bits allocated values for RT Dose
            if bits_allocated not in [16, 32]:
                result.add_error(
                    "Bits Allocated (0028,0100) must be 16 or 32 for RT Dose "
                    "(DICOM PS3.3 C.8.8.3.4.3)"
                )
            
            if bits_stored is not None:
                # For RT Doses, Bits Stored must equal Bits Allocated
                if bits_stored != bits_allocated:
                    result.add_error(
                        "Bits Stored (0028,0101) must equal Bits Allocated for RT Dose "
                        "(DICOM PS3.3 C.8.8.3.4.4)"
                    )
                
                if high_bit is not None:
                    # For RT Doses, High Bit must be one less than Bits Stored
                    if high_bit != bits_stored - 1:
                        result.add_error(
                            "High Bit (0028,0102) must be one less than Bits Stored for RT Dose "
                            "(DICOM PS3.3 C.8.8.3.4.5)"
                        )
        
        # Validate pixel representation based on dose type
        dose_type = getattr(dataset, 'DoseType', '')
        pixel_representation = getattr(dataset, 'PixelRepresentation', None)
        
        if dose_type and pixel_representation is not None:
            if dose_type == 'ERROR' and pixel_representation != 1:
                result.add_error(
                    "Pixel Representation (0028,0103) must be 1 (two's complement) when "
                    "Dose Type is ERROR (DICOM PS3.3 C.8.8.3.4.6)"
                )
            elif dose_type != 'ERROR' and pixel_representation != 0:
                result.add_error(
                    "Pixel Representation (0028,0103) must be 0 (unsigned) when "
                    "Dose Type is not ERROR (DICOM PS3.3 C.8.8.3.4.6)"
                )
        
        # Validate dose grid scaling
        dose_grid_scaling = getattr(dataset, 'DoseGridScaling', None)
        if dose_grid_scaling is not None:
            try:
                scaling_value = float(dose_grid_scaling)
                if scaling_value <= 0:
                    result.add_error(
                        "Dose Grid Scaling (3004,000E) must be positive"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    "Dose Grid Scaling (3004,000E) must be a valid numeric value"
                )
        
        # Validate normalization point format
        normalization_point = getattr(dataset, 'NormalizationPoint', None)
        if normalization_point is not None:
            if len(normalization_point) != 3:
                result.add_error(
                    "Normalization Point (3004,0008) must contain exactly 3 coordinates (x, y, z)"
                )
    
    @staticmethod
    def _validate_cross_field_dependencies(dataset: Dataset, result: ValidationResult) -> None:
        """Validate cross-field dependencies and complex logical relationships."""
        
        # Validate dose summation type consistency with referenced sequences
        dose_summation_type = getattr(dataset, 'DoseSummationType', '')
        
        # Multi-plan validation
        if dose_summation_type == 'MULTI_PLAN':
            rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
            if len(rt_plan_seq) < 2:
                result.add_error(
                    "Referenced RT Plan Sequence (300C,0002) must contain 2 or more items "
                    "when Dose Summation Type is MULTI_PLAN"
                )
        elif dose_summation_type in ['PLAN', 'FRACTION', 'BEAM', 'BRACHY', 
                                    'FRACTION_SESSION', 'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT']:
            rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
            if len(rt_plan_seq) > 1:
                result.add_warning(
                    f"Referenced RT Plan Sequence should contain only a single item "
                    f"when Dose Summation Type is {dose_summation_type}"
                )
        
        # Plan overview sequence count validation
        plan_overview_seq = getattr(dataset, 'PlanOverviewSequence', [])
        if dose_summation_type == 'PLAN_OVERVIEW' and len(plan_overview_seq) == 0:
            result.add_error(
                "Plan Overview Sequence (300C,0116) must contain one or more items "
                "when Dose Summation Type is PLAN_OVERVIEW"
            )
        elif dose_summation_type in ['PLAN', 'RECORD'] and len(plan_overview_seq) > 1:
            result.add_warning(
                f"Plan Overview Sequence should contain only one item "
                f"when Dose Summation Type is {dose_summation_type}"
            )
        elif dose_summation_type == 'MULTI_PLAN' and len(plan_overview_seq) < 2:
            result.add_warning(
                "Plan Overview Sequence should contain two or more items "
                "when Dose Summation Type is MULTI_PLAN"
            )
        
        # Validate Plan Overview Index uniqueness
        plan_indices = []
        for i, plan_overview in enumerate(plan_overview_seq):
            plan_index = plan_overview.get('PlanOverviewIndex')
            if plan_index is not None:
                if plan_index in plan_indices:
                    result.add_error(
                        f"Plan Overview Sequence item {i}: Plan Overview Index ({plan_index}) "
                        "must be unique within the sequence"
                    )
                else:
                    plan_indices.append(plan_index)
        
        # Validate Number of Fractions consistency
        for i, plan_overview in enumerate(plan_overview_seq):
            num_fractions_included = plan_overview.get('NumberOfFractionsIncluded')
            if num_fractions_included is not None and dose_summation_type in ['PLAN', 'MULTI_PLAN']:
                # For PLAN or MULTI_PLAN, this should equal Number of Fractions Planned from RT Plan
                # We can only warn since we don't have access to the actual RT Plan
                result.add_warning(
                    f"Plan Overview Sequence item {i}: Verify that Number of Fractions Included "
                    "equals Number of Fractions Planned from the referenced RT Plan"
                )
        
        # Validate Derivation Code Sequence consistency
        derivation_seq = getattr(dataset, 'DerivationCodeSequence', [])
        for i, derivation in enumerate(derivation_seq):
            if derivation.get('CodeValue') == '121377':  # "Composed with radiobiological effects"
                if dose_type != 'EFFECTIVE':
                    result.add_error(
                        "Dose Type (3004,0004) must be EFFECTIVE when Derivation Code Sequence "
                        "contains 'Composed with radiobiological effects' (DICOM PS3.3 C.8.8.3.6)"
                    )
        
        # Validate grid frame offset vector consistency
        grid_offsets = getattr(dataset, 'GridFrameOffsetVector', None)
        if grid_offsets is not None:
            num_frames = getattr(dataset, 'NumberOfFrames', 1)
            if len(grid_offsets) != num_frames:
                result.add_warning(
                    f"Grid Frame Offset Vector should contain {num_frames} values "
                    f"(one per frame), but contains {len(grid_offsets)} values"
                )
            
            # Check that values vary monotonically
            if len(grid_offsets) > 1:
                is_monotonic = all(grid_offsets[i] <= grid_offsets[i+1] for i in range(len(grid_offsets)-1))
                if not is_monotonic:
                    result.add_warning(
                        "Grid Frame Offset Vector values should vary monotonically "
                        "(DICOM PS3.3 C.8.8.3.2)"
                    )
        
        # Content Date/Time consistency check
        content_date = getattr(dataset, 'ContentDate', None)
        content_time = getattr(dataset, 'ContentTime', None)
        if content_date is not None and content_time is None:
            result.add_warning(
                "Content Time (0008,0033) should be provided when Content Date (0008,0023) is present"
            )
        elif content_time is not None and content_date is None:
            result.add_warning(
                "Content Date (0008,0023) should be provided when Content Time (0008,0033) is present"
            )