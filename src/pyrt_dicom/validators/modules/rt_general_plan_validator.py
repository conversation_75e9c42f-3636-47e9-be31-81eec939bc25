"""RT General Plan Module DICOM validation - PS3.3 C.8.8.9

Validates RT General Plan Module requirements according to DICOM PS3.3 C.8.8.9.
Provides comprehensive validation of Type 1, Type 2, Type 3, and conditional requirements
with clear, actionable error messages for end users.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import PlanIntent, RTplanGeometry, RTPlanRelationship


class RTGeneralPlanValidator(BaseValidator):
    """Validator for DICOM RT General Plan Module (PS3.3 C.8.8.9).

    Validates all requirements from DICOM PS3.3 C.8.8.9 including:
    - Type 1 required elements (RT Plan Label, RT Plan Geometry)
    - Type 2 elements with empty value handling (RT Plan Date, RT Plan Time)
    - Type 1C conditional requirements (Referenced Structure Set Sequence)
    - Enumerated value validation for all coded elements
    - Sequence structure validation with detailed error reporting
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT General Plan Module requirements on any pydicom Dataset.

        Performs comprehensive validation according to DICOM PS3.3 C.8.8.9 including
        Type 1 required elements, Type 2 elements, conditional requirements, and
        sequence structure validation.

        Args:
            dataset: pydicom Dataset to validate against RT General Plan Module requirements
            config: Optional validation configuration to control validation scope

        Returns:
            ValidationResult: Structured result with errors and warnings lists containing
                            specific DICOM tag references and actionable guidance
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 required elements
        RTGeneralPlanValidator._validate_required_elements(dataset, result)

        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            RTGeneralPlanValidator._validate_conditional_requirements(dataset, result)

        # Validate enumerated values
        if config.check_enumerated_values:
            RTGeneralPlanValidator._validate_enumerated_values(dataset, result)

        # Validate sequence structures
        if config.validate_sequences:
            RTGeneralPlanValidator._validate_sequence_requirements(dataset, result)

        return result

    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) elements according to DICOM PS3.3 C.8.8.9."""

        # Type 1: RT Plan Label (300A,0002)
        if not hasattr(dataset, 'RTPlanLabel'):
            result.add_error(
                "RT Plan Label (300A,0002) is required (Type 1). "
                "Provide a user-defined label for the treatment plan."
            )
        elif not dataset.RTPlanLabel:
            result.add_error(
                "RT Plan Label (300A,0002) cannot be empty (Type 1). "
                "Provide a meaningful label for the treatment plan."
            )

        # Type 1: RT Plan Geometry (300A,000C)
        if not hasattr(dataset, 'RTPlanGeometry'):
            result.add_error(
                "RT Plan Geometry (300A,000C) is required (Type 1). "
                "Specify whether the plan is based on PATIENT or TREATMENT_DEVICE geometry."
            )
        elif not dataset.RTPlanGeometry:
            result.add_error(
                "RT Plan Geometry (300A,000C) cannot be empty (Type 1). "
                "Must be either 'PATIENT' or 'TREATMENT_DEVICE'."
            )

    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements according to DICOM PS3.3 C.8.8.9."""

        # Type 1C: Referenced Structure Set Sequence required if RT Plan Geometry is PATIENT
        rt_plan_geometry = getattr(dataset, 'RTPlanGeometry', '')
        if rt_plan_geometry == "PATIENT":
            if not hasattr(dataset, 'ReferencedStructureSetSequence'):
                result.add_error(
                    "Referenced Structure Set Sequence (300C,0060) is required when "
                    "RT Plan Geometry (300A,000C) is PATIENT (Type 1C). "
                    "The RT Structure Set on which the RT Plan is based must be specified. "
                    "See DICOM PS3.3 C.8.8.9.1 for details."
                )
            elif not dataset.ReferencedStructureSetSequence:
                result.add_error(
                    "Referenced Structure Set Sequence (300C,0060) cannot be empty when "
                    "RT Plan Geometry (300A,000C) is PATIENT (Type 1C). "
                    "At least one structure set reference is required."
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM PS3.3 C.8.8.9 specifications."""

        # RT Plan Geometry (300A,000C) - Type 1
        rt_plan_geometry = getattr(dataset, 'RTPlanGeometry', '')
        if rt_plan_geometry:
            valid_geometries = [geometry.value for geometry in RTplanGeometry]
            if rt_plan_geometry not in valid_geometries:
                result.add_error(
                    f"RT Plan Geometry (300A,000C) value '{rt_plan_geometry}' is invalid. "
                    f"Must be one of: {', '.join(valid_geometries)}. "
                    "PATIENT indicates RT Structure Set exists, TREATMENT_DEVICE indicates no patient geometry."
                )

        # Plan Intent (300A,000A) - Type 3
        plan_intent = getattr(dataset, 'PlanIntent', '')
        if plan_intent:
            valid_intents = [intent.value for intent in PlanIntent]
            if plan_intent not in valid_intents:
                result.add_error(
                    f"Plan Intent (300A,000A) value '{plan_intent}' is invalid. "
                    f"Must be one of: {', '.join(valid_intents)}. "
                    "Specify the clinical intent of this treatment plan."
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements according to DICOM PS3.3 C.8.8.9."""

        # Referenced Structure Set Sequence - each item needs SOP Class and Instance UIDs
        ref_structure_set_seq = getattr(dataset, 'ReferencedStructureSetSequence', [])
        for i, item in enumerate(ref_structure_set_seq):
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced Structure Set Sequence item {i+1}: "
                    "Referenced SOP Class UID (0008,1150) is required. "
                    "Specify the SOP Class UID of the referenced RT Structure Set."
                )
            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced Structure Set Sequence item {i+1}: "
                    "Referenced SOP Instance UID (0008,1155) is required. "
                    "Specify the SOP Instance UID of the referenced RT Structure Set."
                )
        
        # Referenced RT Plan Sequence - each item needs SOP references and relationship
        ref_rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
        for i, item in enumerate(ref_rt_plan_seq):
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced RT Plan Sequence item {i+1}: "
                    "Referenced SOP Class UID (0008,1150) is required. "
                    "Specify the SOP Class UID of the referenced RT Plan."
                )
            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced RT Plan Sequence item {i+1}: "
                    "Referenced SOP Instance UID (0008,1155) is required. "
                    "Specify the SOP Instance UID of the referenced RT Plan."
                )

            # Validate RT Plan Relationship enumerated values
            rt_plan_relationship = item.get('RTPlanRelationship', '')
            if not rt_plan_relationship:
                result.add_error(
                    f"Referenced RT Plan Sequence item {i+1}: "
                    "RT Plan Relationship (300A,0055) is required. "
                    "Specify the relationship of the referenced plan to the current plan."
                )
            else:
                valid_relationships = [rel.value for rel in RTPlanRelationship]
                if rt_plan_relationship not in valid_relationships:
                    result.add_error(
                        f"Referenced RT Plan Sequence item {i+1}: "
                        f"RT Plan Relationship (300A,0055) value '{rt_plan_relationship}' is invalid. "
                        f"Must be one of: {', '.join(valid_relationships)}."
                    )
        
        # Treatment Site Code Sequence - each item needs code value, scheme, and meaning
        treatment_site_code_seq = getattr(dataset, 'TreatmentSiteCodeSequence', [])
        for i, item in enumerate(treatment_site_code_seq):
            if not item.get('CodeValue'):
                result.add_error(
                    f"Treatment Site Code Sequence item {i+1}: "
                    "Code Value (0008,0100) is required. "
                    "Provide the anatomical region code value (e.g., from SNOMED CT)."
                )
            if not item.get('CodingSchemeDesignator'):
                result.add_error(
                    f"Treatment Site Code Sequence item {i+1}: "
                    "Coding Scheme Designator (0008,0102) is required. "
                    "Specify the coding scheme (e.g., 'SCT' for SNOMED CT)."
                )
            if not item.get('CodeMeaning'):
                result.add_error(
                    f"Treatment Site Code Sequence item {i+1}: "
                    "Code Meaning (0008,0104) is required. "
                    "Provide human-readable description of the anatomical site."
                )
        
        # Referenced Dose Sequence - each item needs SOP Class and Instance UIDs
        ref_dose_seq = getattr(dataset, 'ReferencedDoseSequence', [])
        for i, item in enumerate(ref_dose_seq):
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced Dose Sequence item {i+1}: "
                    "Referenced SOP Class UID (0008,1150) is required. "
                    "Specify the SOP Class UID of the referenced RT Dose object."
                )
            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced Dose Sequence item {i+1}: "
                    "Referenced SOP Instance UID (0008,1155) is required. "
                    "Specify the SOP Instance UID of the referenced RT Dose object."
                )

        # RT Assertions Sequence validation (basic structure check)
        rt_assertions_seq = getattr(dataset, 'RTAssertionsSequence', [])
        for i, item in enumerate(rt_assertions_seq):
            # Basic validation - specific assertion requirements would depend on the assertion type
            if not any(key in item for key in ['AssertionCodeSequence', 'AssertionUID']):
                result.add_warning(
                    f"RT Assertions Sequence item {i+1}: "
                    "Should contain assertion identification elements (Assertion Code Sequence or Assertion UID). "
                    "See DICOM PS3.3 Table 10.30-1 Assertion Macro for details."
                )
